import { useState } from "react";
import { 
  Edit, 
  Copy, 
  Trash2, 
  MoreVertical, 
  Calendar, 
  Database,
  Layers,
  Settings
} from "lucide-react";
import type { SquibTemplate } from "../../types/squib";

interface TemplateCardProps {
  template: SquibTemplate;
  viewMode: "grid" | "list";
  onEdit: () => void;
  onDuplicate: () => void;
  onDelete: () => void;
}

export function TemplateCard({ 
  template, 
  viewMode, 
  onEdit, 
  onDuplicate, 
  onDelete 
}: TemplateCardProps) {
  const [showMenu, setShowMenu] = useState(false);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const getTemplateStats = () => {
    return {
      components: template.components.length,
      dataFields: template.dataSchema.length,
      layouts: Object.keys(template.layouts).length,
    };
  };

  const stats = getTemplateStats();

  if (viewMode === "list") {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3">
              <div className="w-12 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-md flex items-center justify-center text-white font-bold text-sm">
                {template.name.charAt(0).toUpperCase()}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-semibold text-gray-900 truncate">
                  {template.name}
                </h3>
                <p className="text-gray-600 text-sm line-clamp-2">
                  {template.description}
                </p>
                <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {formatDate(template.updatedAt)}
                  </span>
                  <span className="flex items-center gap-1">
                    <Layers className="w-3 h-3" />
                    {stats.components} components
                  </span>
                  <span className="flex items-center gap-1">
                    <Database className="w-3 h-3" />
                    {stats.dataFields} fields
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2 ml-4">
            <button
              onClick={onEdit}
              className="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Edit
            </button>
            
            <div className="relative">
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="p-1.5 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
              >
                <MoreVertical className="w-4 h-4" />
              </button>
              
              {showMenu && (
                <div className="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                  <button
                    onClick={() => {
                      onDuplicate();
                      setShowMenu(false);
                    }}
                    className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                  >
                    <Copy className="w-4 h-4" />
                    Duplicate
                  </button>
                  <button
                    onClick={() => {
                      onDelete();
                      setShowMenu(false);
                    }}
                    className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                  >
                    <Trash2 className="w-4 h-4" />
                    Delete
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Grid view
  return (
    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow group">
      {/* Template Preview */}
      <div className="aspect-[3/4] bg-gradient-to-br from-blue-500 to-purple-600 relative">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-24 h-32 bg-white rounded-lg shadow-lg flex items-center justify-center">
            <span className="text-2xl font-bold text-gray-400">
              {template.name.charAt(0).toUpperCase()}
            </span>
          </div>
        </div>
        
        {/* Overlay with actions */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
          <div className="flex gap-2">
            <button
              onClick={onEdit}
              className="p-2 bg-white text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
              title="Edit Template"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={onDuplicate}
              className="p-2 bg-white text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
              title="Duplicate Template"
            >
              <Copy className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Template Info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-gray-900 truncate flex-1">
            {template.name}
          </h3>
          <div className="relative ml-2">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
            >
              <MoreVertical className="w-4 h-4" />
            </button>
            
            {showMenu && (
              <div className="absolute right-0 top-full mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                <button
                  onClick={() => {
                    onDuplicate();
                    setShowMenu(false);
                  }}
                  className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                >
                  <Copy className="w-3 h-3" />
                  Duplicate
                </button>
                <button
                  onClick={() => {
                    onDelete();
                    setShowMenu(false);
                  }}
                  className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                >
                  <Trash2 className="w-3 h-3" />
                  Delete
                </button>
              </div>
            )}
          </div>
        </div>
        
        <p className="text-gray-600 text-sm line-clamp-2 mb-3">
          {template.description}
        </p>
        
        {/* Template Stats */}
        <div className="grid grid-cols-3 gap-2 text-xs text-gray-500 mb-3">
          <div className="flex items-center gap-1">
            <Layers className="w-3 h-3" />
            <span>{stats.components}</span>
          </div>
          <div className="flex items-center gap-1">
            <Database className="w-3 h-3" />
            <span>{stats.dataFields}</span>
          </div>
          <div className="flex items-center gap-1">
            <Settings className="w-3 h-3" />
            <span>{stats.layouts}</span>
          </div>
        </div>
        
        {/* Last Updated */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span className="flex items-center gap-1">
            <Calendar className="w-3 h-3" />
            {formatDate(template.updatedAt)}
          </span>
          <span className="text-gray-400">v{template.version}</span>
        </div>
        
        {/* Edit Button */}
        <button
          onClick={onEdit}
          className="w-full mt-3 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
        >
          Edit Template
        </button>
      </div>
    </div>
  );
}
