import { useState } from "react";
import { <PERSON>, <PERSON>rk<PERSON>, <PERSON><PERSON>, FileText } from "lucide-react";
import type { SquibTemplateCategory } from "../../types/squib";

interface CreateTemplateDialogProps {
  onClose: () => void;
  onCreate: (templateData: {
    name: string;
    description: string;
    category: SquibTemplateCategory;
    baseTemplate?: string;
  }) => void;
}

const TEMPLATE_CATEGORIES: { value: SquibTemplateCategory; label: string; description: string }[] = [
  { 
    value: "card-games", 
    label: "Card Games", 
    description: "Trading cards, deck builders, and card-based games" 
  },
  { 
    value: "board-games", 
    label: "Board Games", 
    description: "Tokens, tiles, and board game components" 
  },
  { 
    value: "prototyping", 
    label: "Prototyping", 
    description: "Quick mockups and game prototypes" 
  },
  { 
    value: "educational", 
    label: "Educational", 
    description: "Learning materials and educational content" 
  },
  { 
    value: "business", 
    label: "Business", 
    description: "Business cards, presentations, and corporate materials" 
  },
  { 
    value: "custom", 
    label: "Custom", 
    description: "Unique designs and specialized use cases" 
  },
];

const BASE_TEMPLATES = [
  {
    id: "blank",
    name: "Blank Template",
    description: "Start from scratch with a completely blank canvas",
    icon: FileText,
  },
  {
    id: "economy",
    name: "Economy Card",
    description: "Simple card layout inspired by deck-building games",
    icon: Sparkles,
  },
  {
    id: "standard",
    name: "Standard Card",
    description: "Traditional card layout with title, art, and description",
    icon: Copy,
  },
];

export function CreateTemplateDialog({ onClose, onCreate }: CreateTemplateDialogProps) {
  const [step, setStep] = useState<"details" | "base">("details");
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "prototyping" as SquibTemplateCategory,
    baseTemplate: "blank",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = "Template name is required";
    } else if (formData.name.length < 3) {
      newErrors.name = "Template name must be at least 3 characters";
    }
    
    if (!formData.description.trim()) {
      newErrors.description = "Description is required";
    } else if (formData.description.length < 10) {
      newErrors.description = "Description must be at least 10 characters";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      setStep("base");
    }
  };

  const handleCreate = () => {
    if (validateForm()) {
      onCreate(formData);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Create New Template
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {step === "details" 
                ? "Enter template details and category"
                : "Choose a base template to start with"
              }
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Progress Indicator */}
        <div className="px-6 py-3 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step === "details" ? "bg-blue-600 text-white" : "bg-green-600 text-white"
            }`}>
              1
            </div>
            <div className={`flex-1 h-1 mx-3 ${
              step === "base" ? "bg-green-600" : "bg-gray-300"
            }`} />
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step === "base" ? "bg-blue-600 text-white" : "bg-gray-300 text-gray-600"
            }`}>
              2
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {step === "details" ? (
            <div className="space-y-6">
              {/* Template Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="e.g., My Card Template"
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.name ? "border-red-500" : "border-gray-300"
                  }`}
                />
                {errors.name && (
                  <p className="text-red-600 text-sm mt-1">{errors.name}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  placeholder="Describe what this template is for and how it should be used..."
                  rows={3}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${
                    errors.description ? "border-red-500" : "border-gray-300"
                  }`}
                />
                {errors.description && (
                  <p className="text-red-600 text-sm mt-1">{errors.description}</p>
                )}
              </div>

              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {TEMPLATE_CATEGORIES.map((category) => (
                    <label
                      key={category.value}
                      className={`relative flex items-start p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                        formData.category === category.value
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-300"
                      }`}
                    >
                      <input
                        type="radio"
                        name="category"
                        value={category.value}
                        checked={formData.category === category.value}
                        onChange={(e) => handleInputChange("category", e.target.value)}
                        className="sr-only"
                      />
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">
                          {category.label}
                        </div>
                        <div className="text-sm text-gray-600">
                          {category.description}
                        </div>
                      </div>
                      {formData.category === category.value && (
                        <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full" />
                        </div>
                      )}
                    </label>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Choose Base Template
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  Select a starting point for your template. You can customize everything later.
                </p>
              </div>

              <div className="grid grid-cols-1 gap-4">
                {BASE_TEMPLATES.map((template) => {
                  const Icon = template.icon;
                  return (
                    <label
                      key={template.id}
                      className={`relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                        formData.baseTemplate === template.id
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-300"
                      }`}
                    >
                      <input
                        type="radio"
                        name="baseTemplate"
                        value={template.id}
                        checked={formData.baseTemplate === template.id}
                        onChange={(e) => handleInputChange("baseTemplate", e.target.value)}
                        className="sr-only"
                      />
                      <div className="flex items-center flex-1">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center mr-4 ${
                          formData.baseTemplate === template.id
                            ? "bg-blue-600 text-white"
                            : "bg-gray-200 text-gray-600"
                        }`}>
                          <Icon className="w-5 h-5" />
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">
                            {template.name}
                          </div>
                          <div className="text-sm text-gray-600">
                            {template.description}
                          </div>
                        </div>
                      </div>
                      {formData.baseTemplate === template.id && (
                        <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full" />
                        </div>
                      )}
                    </label>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex gap-3">
            {step === "base" && (
              <button
                onClick={() => setStep("details")}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Back
              </button>
            )}
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
          </div>
          
          <button
            onClick={step === "details" ? handleNext : handleCreate}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {step === "details" ? "Next" : "Create Template"}
          </button>
        </div>
      </div>
    </div>
  );
}
