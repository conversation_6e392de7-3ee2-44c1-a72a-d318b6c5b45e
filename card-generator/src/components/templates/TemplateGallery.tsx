import { useState } from "react";
import { useNavigate } from "@tanstack/react-router";
import { Plus, Search, Filter, Grid, List } from "lucide-react";
import { TemplateCard } from "./TemplateCard";
import { CreateTemplateDialog } from "./CreateTemplateDialog";
import type { SquibTemplate, SquibTemplateCategory } from "../../types/squib";

// Mock data for now - will be replaced with actual data store
const MOCK_TEMPLATES: SquibTemplate[] = [
  {
    id: "economy-card",
    name: "Economy Card",
    description:
      "Simple card template inspired by deck-building games. Perfect for prototyping.",
    deckConfig: {
      cards: 1,
      width: 300,
      height: 400,
      dpi: 300,
    },
    components: [],
    layouts: {},
    dataSchema: [
      { id: "name", name: "Card Name", type: "text", required: true },
      { id: "cost", name: "Cost", type: "number", required: false },
      { id: "description", name: "Description", type: "text", required: false },
    ],
    printSettings: {
      bleed: 3,
      trimMarks: true,
      colorProfile: "RGB",
      dpi: 300,
    },
    version: 1,
    createdAt: Date.now() - 86400000,
    updatedAt: Date.now() - 86400000,
  },
  {
    id: "faction-card",
    name: "Faction Card",
    description:
      "Template for faction-based cards with military power and special abilities.",
    deckConfig: {
      cards: 1,
      width: 300,
      height: 400,
      dpi: 300,
    },
    components: [],
    layouts: {},
    dataSchema: [
      { id: "name", name: "Unit Name", type: "text", required: true },
      { id: "faction", name: "Faction", type: "text", required: true },
      { id: "power", name: "Military Power", type: "number", required: false },
      { id: "ability", name: "Special Ability", type: "text", required: false },
    ],
    printSettings: {
      bleed: 3,
      trimMarks: true,
      colorProfile: "RGB",
      dpi: 300,
    },
    version: 1,
    createdAt: Date.now() - 172800000,
    updatedAt: Date.now() - 172800000,
  },
];

const TEMPLATE_CATEGORIES: { value: SquibTemplateCategory; label: string }[] = [
  { value: "card-games", label: "Card Games" },
  { value: "board-games", label: "Board Games" },
  { value: "prototyping", label: "Prototyping" },
  { value: "educational", label: "Educational" },
  { value: "business", label: "Business" },
  { value: "custom", label: "Custom" },
];

export function TemplateGallery() {
  const navigate = useNavigate();
  const [templates, setTemplates] = useState<SquibTemplate[]>(MOCK_TEMPLATES);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<
    SquibTemplateCategory | "all"
  >("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase());

    // For now, we'll assume all templates are in the "prototyping" category
    const matchesCategory =
      selectedCategory === "all" || selectedCategory === "prototyping";

    return matchesSearch && matchesCategory;
  });

  const handleCreateTemplate = (templateData: {
    name: string;
    description: string;
    category: SquibTemplateCategory;
    baseTemplate?: string;
  }) => {
    const newTemplate: SquibTemplate = {
      id: `template_${Date.now()}`,
      name: templateData.name,
      description: templateData.description,
      deckConfig: {
        cards: 1,
        width: 300,
        height: 400,
        dpi: 300,
      },
      components: [],
      layouts: {},
      dataSchema: [],
      printSettings: {
        bleed: 3,
        trimMarks: true,
        colorProfile: "RGB",
        dpi: 300,
      },
      version: 1,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    setTemplates((prev) => [newTemplate, ...prev]);
    setShowCreateDialog(false);

    // Navigate to template editor
    navigate({ to: `/templates/edit/${newTemplate.id}` });
  };

  const handleEditTemplate = (templateId: string) => {
    // Navigate to template editor
    navigate({ to: `/templates/edit/${templateId}` });
  };

  const handleDuplicateTemplate = (templateId: string) => {
    const template = templates.find((t) => t.id === templateId);
    if (!template) return;

    const duplicatedTemplate: SquibTemplate = {
      ...template,
      id: `template_${Date.now()}`,
      name: `${template.name} (Copy)`,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    setTemplates((prev) => [duplicatedTemplate, ...prev]);
  };

  const handleDeleteTemplate = (templateId: string) => {
    if (confirm("Are you sure you want to delete this template?")) {
      setTemplates((prev) => prev.filter((t) => t.id !== templateId));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 gap-4 items-center">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Category Filter */}
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <select
              value={selectedCategory}
              onChange={(e) =>
                setSelectedCategory(
                  e.target.value as SquibTemplateCategory | "all"
                )
              }
              className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
            >
              <option value="all">All Categories</option>
              {TEMPLATE_CATEGORIES.map((category) => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="flex gap-2 items-center">
          {/* View Mode Toggle */}
          <div className="flex border border-gray-300 rounded-lg overflow-hidden">
            <button
              onClick={() => setViewMode("grid")}
              className={`p-2 ${viewMode === "grid" ? "bg-blue-500 text-white" : "bg-white text-gray-600 hover:bg-gray-50"}`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode("list")}
              className={`p-2 ${viewMode === "list" ? "bg-blue-500 text-white" : "bg-white text-gray-600 hover:bg-gray-50"}`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>

          {/* Create Template Button */}
          <button
            onClick={() => setShowCreateDialog(true)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Create Template
          </button>
        </div>
      </div>

      {/* Templates Grid/List */}
      {filteredTemplates.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Grid className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No templates found
          </h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || selectedCategory !== "all"
              ? "Try adjusting your search or filter criteria."
              : "Get started by creating your first template."}
          </p>
          <button
            onClick={() => setShowCreateDialog(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Create Your First Template
          </button>
        </div>
      ) : (
        <div
          className={
            viewMode === "grid"
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              : "space-y-4"
          }
        >
          {filteredTemplates.map((template) => (
            <TemplateCard
              key={template.id}
              template={template}
              viewMode={viewMode}
              onEdit={() => handleEditTemplate(template.id)}
              onDuplicate={() => handleDuplicateTemplate(template.id)}
              onDelete={() => handleDeleteTemplate(template.id)}
            />
          ))}
        </div>
      )}

      {/* Create Template Dialog */}
      {showCreateDialog && (
        <CreateTemplateDialog
          onClose={() => setShowCreateDialog(false)}
          onCreate={handleCreateTemplate}
        />
      )}
    </div>
  );
}
