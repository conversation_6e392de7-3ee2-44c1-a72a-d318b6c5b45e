import { useState, useEffect } from "react";
// import { SquibCanvas } from "./SquibCanvas";
// import { ComponentPalette } from "./ComponentPalette";
// import { PropertiesPanel } from "./PropertiesPanel";
// import { DataBindingPanel } from "./DataBindingPanel";
// import { LayoutEditor } from "./LayoutEditor";
import {
  Layers,
  Database,
  Settings,
  Layout,
  Upload,
  Download,
  Eye,
  Code,
} from "lucide-react";
import type {
  SquibTemplate,
  SquibComponent,
  SquibDataSource,
} from "../../types/squib";

interface SquibEditorProps {
  templateId: string;
}

// Mock template data - will be replaced with actual data store
const MOCK_TEMPLATE: SquibTemplate = {
  id: "template_1",
  name: "Economy Card Template",
  description: "A simple card template for deck-building games",
  deckConfig: {
    cards: 1,
    width: 300,
    height: 400,
    dpi: 300,
  },
  components: [
    {
      id: "title",
      type: "text",
      name: "Card Title",
      position: { x: 50, y: 50, width: 200, height: 30 },
      textProperties: {
        str: "Sample Card",
        font_size: 18,
        color: "#000000",
        align: "center",
      },
      dataBinding: {
        field: "name",
      },
    },
    {
      id: "description",
      type: "text",
      name: "Description",
      position: { x: 50, y: 200, width: 200, height: 100 },
      textProperties: {
        str: "Card description goes here...",
        font_size: 12,
        color: "#333333",
        align: "left",
      },
      dataBinding: {
        field: "description",
      },
    },
  ],
  layouts: {},
  dataSchema: [
    { id: "name", name: "Card Name", type: "text", required: true },
    { id: "description", name: "Description", type: "text", required: false },
    { id: "cost", name: "Cost", type: "number", required: false },
  ],
  printSettings: {
    bleed: 3,
    trimMarks: true,
    colorProfile: "RGB",
    dpi: 300,
  },
  version: 1,
  createdAt: Date.now(),
  updatedAt: Date.now(),
};

type EditorTab = "design" | "data" | "layouts" | "settings";

export function SquibEditor({ templateId }: SquibEditorProps) {
  const [template, setTemplate] = useState<SquibTemplate>(MOCK_TEMPLATE);
  const [selectedComponentId, setSelectedComponentId] = useState<string | null>(
    null
  );
  const [activeTab, setActiveTab] = useState<EditorTab>("design");
  const [dataSource, setDataSource] = useState<SquibDataSource | null>(null);
  const [previewMode, setPreviewMode] = useState<"design" | "preview">(
    "design"
  );

  // Load template data
  useEffect(() => {
    // TODO: Load actual template data from store
    console.log("Loading template:", templateId);
  }, [templateId]);

  const selectedComponent = template.components.find(
    (c) => c.id === selectedComponentId
  );

  const handleComponentUpdate = (
    componentId: string,
    updates: Partial<SquibComponent>
  ) => {
    setTemplate((prev) => ({
      ...prev,
      components: prev.components.map((comp) =>
        comp.id === componentId ? { ...comp, ...updates } : comp
      ),
      updatedAt: Date.now(),
    }));
  };

  const handleAddComponent = (type: SquibComponent["type"]) => {
    const newComponent: SquibComponent = {
      id: `component_${Date.now()}`,
      type,
      name: `${type.charAt(0).toUpperCase() + type.slice(1)} Component`,
      position: { x: 50, y: 50, width: 100, height: 50 },
    };

    // Add type-specific default properties
    if (type === "text") {
      newComponent.textProperties = {
        str: "New Text",
        font_size: 14,
        color: "#000000",
        align: "left",
      };
    } else if (type === "image") {
      newComponent.imageProperties = {
        file: "",
        alpha: 1,
      };
    }

    setTemplate((prev) => ({
      ...prev,
      components: [...prev.components, newComponent],
      updatedAt: Date.now(),
    }));

    setSelectedComponentId(newComponent.id);
  };

  const handleDeleteComponent = (componentId: string) => {
    setTemplate((prev) => ({
      ...prev,
      components: prev.components.filter((c) => c.id !== componentId),
      updatedAt: Date.now(),
    }));

    if (selectedComponentId === componentId) {
      setSelectedComponentId(null);
    }
  };

  const handleDataUpload = (file: File) => {
    // TODO: Parse CSV/JSON file and create data source
    console.log("Upload data file:", file.name);
  };

  const tabs = [
    { id: "design" as const, label: "Design", icon: Layers },
    { id: "data" as const, label: "Data", icon: Database },
    { id: "layouts" as const, label: "Layouts", icon: Layout },
    { id: "settings" as const, label: "Settings", icon: Settings },
  ];

  return (
    <div className="h-full flex">
      {/* Left Sidebar - Component Palette & Tools */}
      <div className="w-64 bg-gray-50 border-r border-gray-200 flex flex-col">
        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="flex">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center gap-1 px-3 py-3 text-sm font-medium border-b-2 ${
                    activeTab === tab.id
                      ? "border-blue-500 text-blue-600 bg-white"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="hidden sm:inline">{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="flex-1 overflow-y-auto">
          {activeTab === "design" && (
            <div className="p-4">
              <h3 className="font-medium text-gray-900 mb-4">Components</h3>
              <p className="text-sm text-gray-600">
                Component palette coming soon...
              </p>
            </div>
          )}

          {activeTab === "data" && (
            <div className="p-4">
              <h3 className="font-medium text-gray-900 mb-4">Data Binding</h3>
              <p className="text-sm text-gray-600">
                Data binding panel coming soon...
              </p>
            </div>
          )}

          {activeTab === "layouts" && (
            <div className="p-4">
              <h3 className="font-medium text-gray-900 mb-4">Layouts</h3>
              <p className="text-sm text-gray-600">
                Layout editor coming soon...
              </p>
            </div>
          )}

          {activeTab === "settings" && (
            <div className="p-4">
              <h3 className="font-medium text-gray-900 mb-4">
                Template Settings
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Template Name
                  </label>
                  <input
                    type="text"
                    value={template.name}
                    onChange={(e) =>
                      setTemplate((prev) => ({ ...prev, name: e.target.value }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={template.description}
                    onChange={(e) =>
                      setTemplate((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm resize-none"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Canvas Area */}
      <div className="flex-1 flex flex-col">
        {/* Canvas Toolbar */}
        <div className="bg-white border-b border-gray-200 px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <button
                onClick={() =>
                  setPreviewMode(
                    previewMode === "design" ? "preview" : "design"
                  )
                }
                className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm ${
                  previewMode === "preview"
                    ? "bg-blue-100 text-blue-700"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                <Eye className="w-4 h-4" />
                {previewMode === "preview" ? "Exit Preview" : "Preview"}
              </button>
            </div>

            <div className="flex items-center gap-2">
              <button className="flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                <Upload className="w-4 h-4" />
                Import
              </button>
              <button className="flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                <Download className="w-4 h-4" />
                Export
              </button>
              <button className="flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                <Code className="w-4 h-4" />
                Code
              </button>
            </div>
          </div>
        </div>

        {/* Canvas */}
        <div className="flex-1 overflow-hidden bg-gray-100 flex items-center justify-center">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Canvas Coming Soon
            </h3>
            <p className="text-gray-600">
              The visual canvas editor will be implemented next.
            </p>
          </div>
        </div>
      </div>

      {/* Right Sidebar - Properties */}
      <div className="w-80 bg-gray-50 border-l border-gray-200 p-4">
        <h3 className="font-medium text-gray-900 mb-4">Properties</h3>
        <p className="text-sm text-gray-600">Properties panel coming soon...</p>
      </div>
    </div>
  );
}
