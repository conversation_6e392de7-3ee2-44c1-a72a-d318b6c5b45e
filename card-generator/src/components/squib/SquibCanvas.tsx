import { useState, useRef, useCallback, useEffect } from "react";
import { Ruler, Grid, ZoomIn, ZoomOut, RotateCcw } from "lucide-react";
import type { SquibTemplate, SquibComponent, SquibDataSource } from "../../types/squib";

interface SquibCanvasProps {
  template: SquibTemplate;
  selectedComponentId: string | null;
  onSelectComponent: (id: string | null) => void;
  onUpdateComponent: (id: string, updates: Partial<SquibComponent>) => void;
  previewMode: "design" | "preview";
  dataSource: SquibDataSource | null;
}

interface DragState {
  isDragging: boolean;
  dragType: "move" | "resize";
  startX: number;
  startY: number;
  startComponentX: number;
  startComponentY: number;
  startComponentWidth: number;
  startComponentHeight: number;
  resizeHandle?: string;
}

export function SquibCanvas({
  template,
  selectedComponentId,
  onSelectComponent,
  onUpdateComponent,
  previewMode,
  dataSource,
}: SquibCanvasProps) {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [zoom, setZoom] = useState(1);
  const [showGrid, setShowGrid] = useState(true);
  const [showRulers, setShowRulers] = useState(true);
  const [dragState, setDragState] = useState<DragState | null>(null);

  // Card dimensions in pixels (converted from mm at 300 DPI)
  const cardWidth = (template.deckConfig.width || 300) * (template.deckConfig.dpi || 300) / 25.4;
  const cardHeight = (template.deckConfig.height || 400) * (template.deckConfig.dpi || 300) / 25.4;

  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onSelectComponent(null);
    }
  }, [onSelectComponent]);

  const handleComponentClick = useCallback((e: React.MouseEvent, componentId: string) => {
    e.stopPropagation();
    onSelectComponent(componentId);
  }, [onSelectComponent]);

  const handleMouseDown = useCallback((e: React.MouseEvent, component: SquibComponent) => {
    if (previewMode === "preview") return;

    e.preventDefault();
    e.stopPropagation();

    onSelectComponent(component.id);

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    setDragState({
      isDragging: true,
      dragType: "move",
      startX: e.clientX,
      startY: e.clientY,
      startComponentX: typeof component.position.x === "number" ? component.position.x : 0,
      startComponentY: typeof component.position.y === "number" ? component.position.y : 0,
      startComponentWidth: typeof component.position.width === "number" ? component.position.width : 100,
      startComponentHeight: typeof component.position.height === "number" ? component.position.height : 50,
    });
  }, [previewMode, onSelectComponent]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!dragState?.isDragging || !selectedComponentId) return;

    const deltaX = (e.clientX - dragState.startX) / zoom;
    const deltaY = (e.clientY - dragState.startY) / zoom;

    if (dragState.dragType === "move") {
      const newX = Math.max(0, Math.min(cardWidth - dragState.startComponentWidth, dragState.startComponentX + deltaX));
      const newY = Math.max(0, Math.min(cardHeight - dragState.startComponentHeight, dragState.startComponentY + deltaY));

      onUpdateComponent(selectedComponentId, {
        position: {
          x: newX,
          y: newY,
          width: dragState.startComponentWidth,
          height: dragState.startComponentHeight,
        },
      });
    }
  }, [dragState, selectedComponentId, onUpdateComponent, zoom, cardWidth, cardHeight]);

  const handleMouseUp = useCallback(() => {
    setDragState(null);
  }, []);

  useEffect(() => {
    if (dragState?.isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [dragState, handleMouseMove, handleMouseUp]);

  const renderComponent = (component: SquibComponent) => {
    const x = typeof component.position.x === "number" ? component.position.x : 0;
    const y = typeof component.position.y === "number" ? component.position.y : 0;
    const width = typeof component.position.width === "number" ? component.position.width : 100;
    const height = typeof component.position.height === "number" ? component.position.height : 50;

    const isSelected = selectedComponentId === component.id;
    const isDesignMode = previewMode === "design";

    // Get data-bound value if available
    let displayValue = "";
    if (component.dataBinding?.field && dataSource) {
      const fieldData = dataSource.data[component.dataBinding.field];
      if (fieldData && fieldData.length > 0) {
        displayValue = fieldData[0]; // Use first row for preview
      }
    }

    const commonStyle = {
      position: "absolute" as const,
      left: x,
      top: y,
      width,
      height,
      cursor: isDesignMode ? "move" : "default",
      border: isSelected && isDesignMode ? "2px solid #3b82f6" : "1px solid transparent",
      borderRadius: "2px",
    };

    switch (component.type) {
      case "text":
        const textProps = component.textProperties || {};
        const textContent = displayValue || textProps.str || "Text";
        
        return (
          <div
            key={component.id}
            style={{
              ...commonStyle,
              fontSize: textProps.font_size || 14,
              color: textProps.color || "#000000",
              textAlign: textProps.align || "left",
              display: "flex",
              alignItems: textProps.valign === "middle" ? "center" : textProps.valign === "bottom" ? "flex-end" : "flex-start",
              padding: "4px",
              fontFamily: textProps.font || "Arial",
              fontWeight: textProps.font?.includes("bold") ? "bold" : "normal",
              fontStyle: textProps.font?.includes("italic") ? "italic" : "normal",
              transform: textProps.angle ? `rotate(${textProps.angle}deg)` : undefined,
              backgroundColor: isSelected && isDesignMode ? "rgba(59, 130, 246, 0.1)" : "transparent",
            }}
            onClick={(e) => handleComponentClick(e, component.id)}
            onMouseDown={(e) => handleMouseDown(e, component)}
          >
            {textContent}
          </div>
        );

      case "image":
        const imageProps = component.imageProperties || {};
        
        return (
          <div
            key={component.id}
            style={{
              ...commonStyle,
              backgroundColor: "#f3f4f6",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              opacity: imageProps.alpha || 1,
            }}
            onClick={(e) => handleComponentClick(e, component.id)}
            onMouseDown={(e) => handleMouseDown(e, component)}
          >
            {imageProps.file ? (
              <img
                src={imageProps.file}
                alt="Component"
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                  borderRadius: "2px",
                }}
              />
            ) : (
              <div className="text-gray-500 text-sm">Image</div>
            )}
          </div>
        );

      case "rect":
        const shapeProps = component.shapeProperties || {};
        
        return (
          <div
            key={component.id}
            style={{
              ...commonStyle,
              backgroundColor: shapeProps.fill_color || "transparent",
              border: `${shapeProps.stroke_width || 1}px solid ${shapeProps.stroke_color || "#000000"}`,
              borderRadius: shapeProps.radius || 0,
            }}
            onClick={(e) => handleComponentClick(e, component.id)}
            onMouseDown={(e) => handleMouseDown(e, component)}
          />
        );

      default:
        return (
          <div
            key={component.id}
            style={{
              ...commonStyle,
              backgroundColor: "#f3f4f6",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              border: "1px dashed #9ca3af",
            }}
            onClick={(e) => handleComponentClick(e, component.id)}
            onMouseDown={(e) => handleMouseDown(e, component)}
          >
            <span className="text-gray-500 text-xs">{component.type}</span>
          </div>
        );
    }
  };

  return (
    <div className="h-full bg-gray-100 relative overflow-hidden">
      {/* Canvas Toolbar */}
      <div className="absolute top-4 left-4 z-10 flex items-center gap-2 bg-white rounded-lg shadow-sm border border-gray-200 p-2">
        <button
          onClick={() => setShowGrid(!showGrid)}
          className={`p-1.5 rounded ${showGrid ? "bg-blue-100 text-blue-600" : "text-gray-600 hover:bg-gray-100"}`}
          title="Toggle Grid"
        >
          <Grid className="w-4 h-4" />
        </button>
        <button
          onClick={() => setShowRulers(!showRulers)}
          className={`p-1.5 rounded ${showRulers ? "bg-blue-100 text-blue-600" : "text-gray-600 hover:bg-gray-100"}`}
          title="Toggle Rulers"
        >
          <Ruler className="w-4 h-4" />
        </button>
        <div className="w-px h-6 bg-gray-300" />
        <button
          onClick={() => setZoom(Math.max(0.25, zoom - 0.25))}
          className="p-1.5 rounded text-gray-600 hover:bg-gray-100"
          title="Zoom Out"
        >
          <ZoomOut className="w-4 h-4" />
        </button>
        <span className="text-sm text-gray-600 min-w-[3rem] text-center">
          {Math.round(zoom * 100)}%
        </span>
        <button
          onClick={() => setZoom(Math.min(3, zoom + 0.25))}
          className="p-1.5 rounded text-gray-600 hover:bg-gray-100"
          title="Zoom In"
        >
          <ZoomIn className="w-4 h-4" />
        </button>
        <button
          onClick={() => setZoom(1)}
          className="p-1.5 rounded text-gray-600 hover:bg-gray-100"
          title="Reset Zoom"
        >
          <RotateCcw className="w-4 h-4" />
        </button>
      </div>

      {/* Canvas Container */}
      <div 
        className="h-full overflow-auto"
        style={{ 
          backgroundImage: showGrid ? 
            `radial-gradient(circle, #e5e7eb 1px, transparent 1px)` : 
            undefined,
          backgroundSize: showGrid ? `${20 * zoom}px ${20 * zoom}px` : undefined,
        }}
      >
        <div className="flex items-center justify-center min-h-full p-8">
          {/* Card Canvas */}
          <div
            ref={canvasRef}
            className="relative bg-white shadow-lg"
            style={{
              width: cardWidth * zoom,
              height: cardHeight * zoom,
              transform: `scale(${zoom})`,
              transformOrigin: "center",
            }}
            onClick={handleCanvasClick}
          >
            {/* Bleed Area Indicator */}
            <div
              className="absolute inset-0 border-2 border-dashed border-red-300 pointer-events-none"
              style={{
                margin: (template.printSettings.bleed * (template.deckConfig.dpi || 300) / 25.4),
              }}
            />

            {/* Components */}
            {template.components.map(renderComponent)}

            {/* Selection Indicator */}
            {selectedComponentId && previewMode === "design" && (
              <div className="absolute inset-0 pointer-events-none">
                {/* Selection handles would go here */}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
