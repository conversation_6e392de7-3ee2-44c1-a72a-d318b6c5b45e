import { createFileRoute } from "@tanstack/react-router";
import { SquibEditor } from "../../../components/squib/SquibEditor";

export const Route = createFileRoute("/templates/edit/$templateId")({
  component: TemplateEditComponent,
});

function TemplateEditComponent() {
  const { templateId } = Route.useParams();

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">
              Template Editor
            </h1>
            <p className="text-sm text-gray-600">
              Editing template: {templateId}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <button className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50">
              Preview
            </button>
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
              Save
            </button>
          </div>
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1 overflow-hidden">
        <SquibEditor templateId={templateId} />
      </div>
    </div>
  );
}
