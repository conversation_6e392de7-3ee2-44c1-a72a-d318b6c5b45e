import { TemplateGallery } from "@/components/templates/TemplateGallery";
import { createFileRoute } from "@tanstack/react-router";

export const Route = createFileRoute("/templates/")({
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Card Templates
        </h1>
        <p className="text-gray-600">
          Create and manage card templates with our Squib-inspired visual
          editor. Design once, generate hundreds of cards from your data.
        </p>
      </div>
      <TemplateGallery />
    </div>
  );
}
