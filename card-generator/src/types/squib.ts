// Squib-inspired types for the web-based card template creator

import type { CardDimensions, CardOrientation } from "./card";
import type { DataFieldDefinition, LayoutDefinition, PrintSettings } from "./customCard";

// Core Squib component types
export type SquibComponentType = 
  | "text" 
  | "image" 
  | "rect" 
  | "circle" 
  | "line" 
  | "background"
  | "svg"
  | "png";

// Squib-style positioning and sizing
export interface SquibPosition {
  x: number | string; // Can be number (pixels) or string ("50%", "center", etc.)
  y: number | string;
  width?: number | string;
  height?: number | string;
}

// Squib-style text properties (inspired by Pango)
export interface SquibTextProperties {
  str?: string | string[]; // Text content, can be array for multiple cards
  font?: string;
  font_size?: number;
  color?: string;
  align?: "left" | "center" | "right" | "justify";
  valign?: "top" | "middle" | "bottom";
  markup?: boolean; // Enable Pango markup
  wrap?: boolean;
  ellipsize?: boolean;
  angle?: number; // Text rotation
  stroke_color?: string;
  stroke_width?: number;
}

// Squib-style image properties
export interface SquibImageProperties {
  file?: string | string[]; // Image file path/URL, can be array
  range?: number | number[] | "all"; // Which cards to apply to
  alpha?: number; // Opacity (0-1)
  blend?: string; // Blend mode
  crop_x?: number;
  crop_y?: number;
  crop_width?: number;
  crop_height?: number;
  flip_horizontal?: boolean;
  flip_vertical?: boolean;
}

// Squib-style shape properties
export interface SquibShapeProperties {
  fill_color?: string;
  stroke_color?: string;
  stroke_width?: number;
  stroke_strategy?: "fill_first" | "stroke_first";
  dash?: string; // Dash pattern
  cap?: "butt" | "round" | "square";
  join?: "miter" | "round" | "bevel";
  radius?: number; // For rounded rectangles
}

// Squib component definition
export interface SquibComponent {
  id: string;
  type: SquibComponentType;
  name: string;
  
  // Position and size
  position: SquibPosition;
  
  // Type-specific properties
  textProperties?: SquibTextProperties;
  imageProperties?: SquibImageProperties;
  shapeProperties?: SquibShapeProperties;
  
  // Data binding
  dataBinding?: {
    field?: string; // Field path like "name", "cost.gold"
    range?: number | number[] | "all"; // Which cards this applies to
    transform?: string; // Transformation function
  };
  
  // Layout reference
  layout?: string; // Reference to a layout definition
  
  // Visibility and layering
  visible?: boolean;
  z_index?: number;
}

// Squib deck configuration (like Squib::Deck.new)
export interface SquibDeckConfig {
  cards: number; // Number of cards in deck
  width?: number; // Card width in points
  height?: number; // Card height in points
  dpi?: number;
  layout?: string | Record<string, LayoutDefinition>; // Layout file or inline layouts
  config?: string; // Config file path
}

// Squib template (equivalent to a .rb file)
export interface SquibTemplate {
  id: string;
  name: string;
  description: string;
  
  // Deck configuration
  deckConfig: SquibDeckConfig;
  
  // Components (equivalent to Squib method calls)
  components: SquibComponent[];
  
  // Layout definitions (equivalent to YAML layout files)
  layouts: Record<string, LayoutDefinition>;
  
  // Data schema for CSV binding
  dataSchema: DataFieldDefinition[];
  
  // Print and export settings
  printSettings: PrintSettings;
  
  // Sample data for preview
  sampleData?: Record<string, any[]>;
  
  // Metadata
  version: number;
  createdAt: number;
  updatedAt: number;
}

// Data source types (CSV, JSON, etc.)
export interface SquibDataSource {
  type: "csv" | "json" | "xlsx" | "manual";
  data: Record<string, any[]>; // Column name -> array of values
  file?: File; // Original file if uploaded
}

// Range specification (Squib-style)
export type SquibRange = 
  | number 
  | number[] 
  | "all" 
  | { start: number; end: number }
  | { except: number | number[] };

// Squib method call representation
export interface SquibMethodCall {
  method: string; // e.g., "text", "png", "rect"
  args: Record<string, any>; // Method arguments
  range?: SquibRange; // Which cards to apply to
}

// Export formats (like Squib's save methods)
export interface SquibExportOptions {
  format: "png" | "pdf" | "svg" | "sheet";
  
  // PNG options
  prefix?: string;
  count_format?: string; // e.g., "%02d"
  
  // PDF options
  file?: string;
  margin?: number;
  gap?: number;
  trim?: number;
  
  // Sheet options
  columns?: number;
  rows?: number;
  
  // Common options
  range?: SquibRange;
  rotate?: boolean;
  trim_radius?: number;
}

// Built-in Squib-style layouts (like economy.yml)
export const SQUIB_BUILTIN_LAYOUTS = {
  economy: {
    title: { x: 38, y: 38, width: 300, height: 26, font_size: 18, align: "center" },
    type: { x: 38, y: 62, width: 300, height: 20, font_size: 12, align: "center" },
    art: { x: 38, y: 86, width: 300, height: 150 },
    description: { x: 38, y: 250, width: 300, height: 115, font_size: 11 },
    cost: { x: 38, y: 38, width: 26, height: 26, font_size: 16, align: "center" },
  },
  
  standard: {
    title: { x: 50, y: 50, width: 280, height: 30, font_size: 20, align: "center" },
    subtitle: { x: 50, y: 85, width: 280, height: 20, font_size: 14, align: "center" },
    art: { x: 50, y: 110, width: 280, height: 180 },
    description: { x: 50, y: 300, width: 280, height: 100, font_size: 12 },
    cost: { x: 20, y: 20, width: 30, height: 30, font_size: 16, align: "center" },
    stats: { x: 320, y: 20, width: 30, height: 30, font_size: 16, align: "center" },
  },
  
  minimal: {
    title: { x: 25, y: 25, width: 325, height: 40, font_size: 22, align: "center" },
    content: { x: 25, y: 75, width: 325, height: 400, font_size: 14 },
  }
} as const;

// Squib color definitions (like config.yml colors)
export const SQUIB_COLORS = {
  // Basic colors
  black: "#000000",
  white: "#ffffff",
  red: "#ff0000",
  green: "#00ff00",
  blue: "#0000ff",
  yellow: "#ffff00",
  cyan: "#00ffff",
  magenta: "#ff00ff",
  
  // Game-specific colors
  gold: "#ffd700",
  silver: "#c0c0c0",
  bronze: "#cd7f32",
  
  // Card colors
  common: "#ffffff",
  uncommon: "#1eff00",
  rare: "#0070dd",
  epic: "#a335ee",
  legendary: "#ff8000",
} as const;

// Unit conversion utilities (Squib supports various units)
export interface SquibUnits {
  mm: (value: number) => number;
  cm: (value: number) => number;
  in: (value: number) => number;
  pt: (value: number) => number;
  px: (value: number) => number;
}

// Squib-style gradient definitions
export interface SquibGradient {
  type: "linear" | "radial";
  stops: Array<{
    offset: number; // 0-1
    color: string;
  }>;
  angle?: number; // For linear gradients
  center?: { x: number; y: number }; // For radial gradients
}

// Template categories for organization
export type SquibTemplateCategory = 
  | "card-games"
  | "board-games" 
  | "prototyping"
  | "educational"
  | "business"
  | "custom";

// Template metadata for gallery
export interface SquibTemplateMetadata {
  id: string;
  name: string;
  description: string;
  category: SquibTemplateCategory;
  tags: string[];
  author?: string;
  thumbnail?: string;
  difficulty: "beginner" | "intermediate" | "advanced";
  estimatedTime: number; // minutes to complete
  cardCount: number;
  features: string[]; // e.g., ["data-binding", "layouts", "images"]
}
