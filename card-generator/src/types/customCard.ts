import type { CardOrientation, CardAsset } from "./card";

// Data binding types
export type DataBindingType = "constant" | "field" | "expression";

export interface DataBinding {
  type: DataBindingType;
  value: string; // For constant values or field paths like "data.name"
  expression?: string; // For complex expressions
}

// Component types for the visual editor
export type ComponentType = "text" | "image" | "shape" | "background";

export interface BaseComponent {
  id: string;
  type: ComponentType;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  opacity: number;
  visible: boolean;
  locked: boolean;
  dataBinding?: DataBinding;
}

export interface TextComponentProperties {
  text?: string;
  fontSize?: number;
  fontFamily?: string;
  fontWeight?: "normal" | "bold" | "lighter" | "bolder";
  fontStyle?: "normal" | "italic";
  color?: string;
  textAlign?: "left" | "center" | "right" | "justify";
  verticalAlign?: "top" | "middle" | "bottom";
  lineHeight?: number;
  letterSpacing?: number;
  textDecoration?: "none" | "underline" | "line-through";
}

export interface ImageComponentProperties {
  src?: string;
  alt?: string;
  fit?: "cover" | "contain" | "fill" | "scale-down" | "none";
  borderRadius?: number;
  border?: string;
}

export interface ShapeComponentProperties {
  shapeType?: "rectangle" | "circle" | "line";
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  borderRadius?: number;
}

export interface BackgroundComponentProperties {
  backgroundColor?: string;
  backgroundImage?: string;
  backgroundSize?: "cover" | "contain" | "auto";
  backgroundPosition?: string;
  backgroundRepeat?: "no-repeat" | "repeat" | "repeat-x" | "repeat-y";
}

export interface DesignComponent extends BaseComponent {
  properties?: TextComponentProperties & ImageComponentProperties & ShapeComponentProperties & BackgroundComponentProperties;
}

// Layout system (Squib-inspired)
export interface LayoutDefinition {
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  fontSize?: number;
  fontFamily?: string;
  fontWeight?: string;
  color?: string;
  textAlign?: string;
  extends?: string; // Layout inheritance
}

// Data field definitions for CSV/JSON binding
export interface DataFieldDefinition {
  id: string;
  name: string;
  type: "text" | "number" | "image" | "boolean";
  required?: boolean;
  defaultValue?: any;
  description?: string;
}

// Print settings
export interface PrintSettings {
  bleed: number;
  trimMarks: boolean;
  colorProfile?: "RGB" | "CMYK";
  dpi: number;
}

// Card dimensions
export interface CardDimensions {
  width: number;
  height: number;
  bleed: number;
  dpi: number;
}

// Virtual DOM system for advanced layouts
export type LayoutMethod = "absolute" | "flexbox" | "grid";

export interface CustomCardElement {
  id: string;
  type: "container" | "text" | "image" | "shape";
  name: string;
  children?: string[]; // IDs of child elements
  layout: LayoutMethod;
  properties: Record<string, any>;
  dataBinding?: DataBinding;
  styles: Record<string, any>;
}

export interface CustomField {
  id: string;
  name: string;
  type: "text" | "number" | "image" | "boolean" | "select";
  options?: string[]; // For select fields
  defaultValue?: any;
  required?: boolean;
  description?: string;
}

// Main custom card type interface
export interface CustomCardType {
  id: string;
  name: string;
  description: string;
  dimensions: CardDimensions;
  orientation: CardOrientation;
  
  // Component-based design (Figma-like)
  components: DesignComponent[];
  
  // Virtual DOM system (advanced)
  elements: CustomCardElement[];
  rootElementId: string;
  fields: CustomField[];
  
  // Layout system (Squib-inspired)
  layouts: Record<string, LayoutDefinition>;
  
  // Data schema for CSV/JSON binding
  dataSchema: DataFieldDefinition[];
  
  // Print settings
  printSettings: PrintSettings;
  
  // Metadata
  version: number;
  createdAt: number;
  updatedAt: number;
}

// Custom card instance
export interface CustomCardData {
  [fieldId: string]: any;
}

export interface CustomCard {
  id: string;
  name: string;
  category: "custom";
  customCardTypeId: string;
  data: CustomCardData;

  // Standard card properties
  illustrationImage?: CardAsset;
  backImage?: CardAsset;
  orientation?: CardOrientation;
  createdAt: number;
  updatedAt: number;
}

// Template for creating new custom card types
export const DEFAULT_CUSTOM_CARD_TYPE: Omit<
  CustomCardType,
  "id" | "createdAt" | "updatedAt"
> = {
  name: "New Card Type",
  description: "",
  dimensions: {
    width: 63, // Standard poker card width in mm
    height: 88, // Standard poker card height in mm
    bleed: 3,
    dpi: 300,
  },
  orientation: "portrait",
  components: [],
  elements: [],
  rootElementId: "",
  fields: [],
  layouts: {},
  dataSchema: [],
  printSettings: {
    bleed: 3,
    trimMarks: true,
    colorProfile: "RGB",
    dpi: 300,
  },
  version: 1,
};

// Squib-inspired template interface
export interface SquibTemplate {
  id: string;
  name: string;
  description: string;
  layouts: Record<string, LayoutDefinition>;
  components: DesignComponent[];
  dataSchema: DataFieldDefinition[];
  printSettings: PrintSettings;
  version: number;
  createdAt: number;
  updatedAt: number;
}

// Built-in layout presets (inspired by Squib's economy.yml, etc.)
export const BUILTIN_LAYOUTS: Record<string, Record<string, LayoutDefinition>> = {
  economy: {
    title: {
      x: 50,
      y: 50,
      width: 200,
      height: 30,
      fontSize: 16,
      fontWeight: "bold",
      textAlign: "center",
    },
    description: {
      x: 50,
      y: 200,
      width: 200,
      height: 100,
      fontSize: 12,
      textAlign: "left",
    },
    cost: {
      x: 20,
      y: 20,
      width: 30,
      height: 30,
      fontSize: 14,
      fontWeight: "bold",
      textAlign: "center",
    },
    art: {
      x: 50,
      y: 90,
      width: 200,
      height: 100,
    },
  },
  standard: {
    title: {
      x: 40,
      y: 40,
      width: 220,
      height: 35,
      fontSize: 18,
      fontWeight: "bold",
      textAlign: "center",
    },
    subtitle: {
      x: 40,
      y: 80,
      width: 220,
      height: 20,
      fontSize: 12,
      textAlign: "center",
    },
    description: {
      x: 40,
      y: 180,
      width: 220,
      height: 120,
      fontSize: 11,
      textAlign: "left",
    },
    art: {
      x: 40,
      y: 105,
      width: 220,
      height: 70,
    },
  },
};
