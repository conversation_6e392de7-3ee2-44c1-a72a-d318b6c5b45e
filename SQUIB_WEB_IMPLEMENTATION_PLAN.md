# Squib-Inspired Web-Based Card Template Creator

## Implementation Plan & Todo List

### Overview

Create a visual, web-based card template creator inspired by <PERSON><PERSON><PERSON> (Ruby DSL for card game prototyping) that allows users to:

- Create custom card templates with live preview
- Use data-driven approach (CSV/JSON)
- Export to various formats (PNG, PDF, print-ready sheets)
- Maintain the core Squib philosophy: DRY, data-driven, flexible layouts

### Core Squib Concepts to Implement

1. **Data-Driven Design**: Templates bind to data sources (CSV, JSON)
2. **Layout System**: Reusable layout definitions (like Squib's YAML layouts)
3. **Component-Based**: Text, images, shapes, backgrounds
4. **Flexible Ranges**: Apply different styles to different card ranges
5. **Print-Ready Output**: Bleed areas, trim lines, sheet layouts
6. **Live Preview**: Real-time visual feedback
7. **Template Inheritance**: Extend and modify existing templates

---

## Phase 1: Foundation & Core Architecture (Week 1-2)

### 1.1 Update Templates Route Structure

- [x] Implement main templates page with template gallery
- [x] Add template creation dialog
- [x] Set up routing for template editor (`/templates/:id/edit`)
- [x] Create template management (CRUD operations)

### 1.2 Enhanced Data Models

- [x] Extend `CustomCardType` to support Squib-like features:
  - [x] Layout definitions (similar to Squib's YAML layouts)
  - [x] Data binding configurations
  - [x] Print settings (bleed, trim, DPI, CMYK)
  - [x] Component inheritance system
- [x] Create `SquibTemplate` interface:
  ```typescript
  interface SquibTemplate {
    id: string;
    name: string;
    description: string;
    layouts: Record<string, LayoutDefinition>;
    components: SquibComponent[];
    dataSchema: DataFieldDefinition[];
    printSettings: PrintSettings;
    version: number;
  }
  ```

### 1.3 Core Components System

- [x] Create base `SquibComponent` types:
  - [x] `TextComponent` (with Pango-like text rendering)
  - [x] `ImageComponent` (PNG, SVG support)
  - [x] `ShapeComponent` (rectangles, circles, lines)
  - [x] `BackgroundComponent`
  - [x] `DataFieldComponent` (binds to data sources)

---

## Phase 2: Visual Editor Implementation (Week 3-4)

### 2.1 Canvas-Based Editor

- [x] Implement main canvas area with:
  - [x] Zoom and pan controls
  - [x] Grid and snap-to-grid
  - [x] Rulers with measurements
  - [x] Bleed and trim area visualization
  - [ ] Multi-card preview mode

### 2.2 Component Manipulation

- [x] Drag and drop from component palette
- [ ] Visual resizing handles
- [ ] Rotation controls
- [x] Layer management (bring to front/back)
- [x] Copy/paste/duplicate components
- [ ] Alignment tools (align left, center, distribute, etc.)

### 2.3 Properties Panel

- [x] Context-sensitive property editing
- [x] Data binding interface
- [x] Style controls (fonts, colors, borders)
- [ ] Layout assignment dropdown
- [x] Component-specific settings

---

## Phase 3: Data Integration & Binding (Week 5)

### 3.1 Data Source Management

- [x] CSV file upload and parsing
- [x] JSON data input
- [x] Manual data entry interface
- [ ] Data validation and type checking
- [ ] Sample data generation for testing

### 3.2 Data Binding System

- [x] Field path binding (e.g., `data.name`, `data.stats.power`)
- [ ] Conditional rendering (show/hide based on data)
- [ ] Array handling for multiple cards
- [ ] Data transformation functions (uppercase, formatting, etc.)
- [ ] Range-based styling (different styles for different card ranges)

### 3.3 Live Data Preview

- [x] Real-time data binding preview
- [ ] Card range navigation
- [ ] Data-driven component visibility
- [x] Dynamic text and image updates

---

## Phase 4: Layout System (Week 6)

### 4.1 Layout Definitions

- [ ] Create layout definition interface similar to Squib's YAML:
  ```yaml
  title:
    x: 50
    y: 50
    width: 200
    height: 30
    font_size: 16
    align: center
  ```
- [ ] Layout inheritance and extension
- [ ] Built-in layout presets (economy, standard, custom)
- [ ] Layout validation and error handling

### 4.2 Layout Management

- [ ] Layout editor interface
- [ ] Layout library/gallery
- [ ] Import/export layouts
- [ ] Layout versioning
- [ ] Layout sharing between templates

---

## Phase 5: Export & Print Features (Week 7)

### 5.1 Export Formats

- [ ] Individual card PNG export
- [ ] PDF generation with proper bleed
- [ ] Print sheet layouts (multiple cards per page)
- [ ] SVG export for vector graphics
- [ ] High-resolution export options

### 5.2 Print-Ready Features

- [ ] Bleed area configuration
- [ ] Trim marks and cut lines
- [ ] Color profile management
- [ ] Print optimization settings
- [ ] Batch export for entire decks

### 5.3 Integration with Existing System

- [ ] Export to current card system
- [ ] Import from existing card types
- [ ] Maintain compatibility with GameCardRenderer

---

## Phase 6: Advanced Features (Week 8-9)

### 6.1 Template Library

- [ ] Built-in template gallery
- [ ] Community template sharing
- [ ] Template categories and tags
- [ ] Template search and filtering
- [ ] Template versioning and updates

### 6.2 Advanced Components

- [ ] Chart/graph components
- [ ] QR code generation
- [ ] Barcode support
- [ ] Mathematical expression rendering
- [ ] Custom shape drawing tools

### 6.3 Workflow Features

- [ ] Template history and undo/redo
- [ ] Collaborative editing
- [ ] Template comments and annotations
- [ ] Auto-save functionality
- [ ] Template backup and restore

---

## Phase 7: Performance & Polish (Week 10)

### 7.1 Performance Optimization

- [ ] Canvas rendering optimization
- [ ] Large dataset handling
- [ ] Image loading and caching
- [ ] Memory management for complex templates
- [ ] Progressive loading for large card sets

### 7.2 User Experience

- [ ] Keyboard shortcuts
- [ ] Context menus
- [ ] Tooltips and help system
- [ ] Error handling and user feedback
- [ ] Mobile responsiveness (view-only)

### 7.3 Testing & Documentation

- [ ] Unit tests for core functionality
- [ ] Integration tests for data binding
- [ ] User acceptance testing
- [ ] Documentation and tutorials
- [ ] Video guides for complex features

---

## Technical Implementation Details

### Key Libraries & Technologies

- **Canvas Rendering**: HTML5 Canvas or SVG for precise control
- **Data Handling**: Papa Parse for CSV, custom JSON parser
- **Export**: jsPDF for PDF generation, html2canvas for PNG
- **UI Components**: Continue with existing React setup
- **State Management**: Zustand for template state
- **File Handling**: File API for uploads, FileSaver.js for downloads

### File Structure

```
src/
├── components/
│   ├── squib/
│   │   ├── SquibEditor.tsx
│   │   ├── SquibCanvas.tsx
│   │   ├── ComponentPalette.tsx
│   │   ├── PropertiesPanel.tsx
│   │   ├── DataBindingPanel.tsx
│   │   └── LayoutEditor.tsx
│   └── templates/
│       ├── TemplateGallery.tsx
│       ├── TemplateCard.tsx
│       └── CreateTemplateDialog.tsx
├── types/
│   ├── squib.ts
│   └── layouts.ts
├── utils/
│   ├── squibExport.ts
│   ├── dataBinding.ts
│   └── layoutEngine.ts
└── routes/
    ├── templates.tsx
    └── templates/
        ├── $templateId.tsx
        └── $templateId.edit.tsx
```

### Integration Points

- Extend existing `CustomCardType` system
- Maintain compatibility with current card rendering
- Integrate with existing deck management
- Preserve current export functionality

---

## Success Metrics

- [ ] Create a card template in under 5 minutes
- [ ] Import CSV data and see live preview
- [ ] Export print-ready PDF with proper bleed
- [ ] Template reusability across different games
- [ ] Performance: Handle 100+ card datasets smoothly
- [ ] User satisfaction: Intuitive interface for non-programmers

---

## Future Enhancements (Post-MVP)

- [ ] Scripting system for advanced users (JavaScript-based)
- [ ] Plugin system for custom components
- [ ] Integration with print-on-demand services
- [ ] Template marketplace
- [ ] AI-assisted layout suggestions
- [ ] Version control for templates
- [ ] Multi-language support for international games

This plan transforms the current basic templates page into a comprehensive, Squib-inspired card creation system while maintaining the visual, user-friendly approach that web interfaces provide.
